#!/usr/bin/env python3
"""
Video Splitter Script
Splits a video file into 1.5-minute (90-second) segments.

Usage:
    python video_splitter.py input_video.mp4
    python video_splitter.py input_video.mp4 --output-dir segments
    python video_splitter.py input_video.mp4 --segment-duration 120
"""

import os
import sys
import argparse
from pathlib import Path
from moviepy.editor import VideoFileClip


def split_video(input_path, output_dir="segments", segment_duration=90):
    """
    Split a video into segments of specified duration.
    
    Args:
        input_path (str): Path to the input video file
        output_dir (str): Directory to save the segments
        segment_duration (int): Duration of each segment in seconds (default: 90 = 1.5 minutes)
    """
    # Validate input file
    if not os.path.exists(input_path):
        print(f"Error: Input file '{input_path}' not found.")
        return False
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    try:
        # Load the video
        print(f"Loading video: {input_path}")
        video = VideoFileClip(input_path)
        
        # Get video duration
        total_duration = video.duration
        print(f"Video duration: {total_duration:.2f} seconds ({total_duration/60:.2f} minutes)")
        
        # Calculate number of segments
        num_segments = int(total_duration // segment_duration) + (1 if total_duration % segment_duration > 0 else 0)
        print(f"Will create {num_segments} segments of {segment_duration} seconds each")
        
        # Get input filename without extension for naming segments
        input_name = Path(input_path).stem
        input_ext = Path(input_path).suffix
        
        # Split the video
        for i in range(num_segments):
            start_time = i * segment_duration
            end_time = min((i + 1) * segment_duration, total_duration)
            
            print(f"Creating segment {i+1}/{num_segments}: {start_time}s - {end_time}s")
            
            # Extract segment
            segment = video.subclip(start_time, end_time)
            
            # Generate output filename
            output_filename = f"{input_name}_segment_{i+1:03d}{input_ext}"
            output_filepath = output_path / output_filename
            
            # Write segment to file
            segment.write_videofile(
                str(output_filepath),
                codec='libx264',
                audio_codec='aac',
                verbose=False,
                logger=None
            )
            
            print(f"Saved: {output_filepath}")
            
            # Close the segment to free memory
            segment.close()
        
        # Close the original video
        video.close()
        
        print(f"\nSuccessfully split video into {num_segments} segments!")
        print(f"Segments saved in: {output_path.absolute()}")
        return True
        
    except Exception as e:
        print(f"Error processing video: {str(e)}")
        return False


def main():
    parser = argparse.ArgumentParser(
        description="Split a video file into 1.5-minute segments",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python video_splitter.py video.mp4
  python video_splitter.py video.mp4 --output-dir my_segments
  python video_splitter.py video.mp4 --segment-duration 120
        """
    )
    
    parser.add_argument(
        "input_video",
        help="Path to the input video file"
    )
    
    parser.add_argument(
        "--output-dir", "-o",
        default="segments",
        help="Output directory for segments (default: segments)"
    )
    
    parser.add_argument(
        "--segment-duration", "-d",
        type=int,
        default=90,
        help="Duration of each segment in seconds (default: 90 = 1.5 minutes)"
    )
    
    args = parser.parse_args()
    
    # Check if moviepy is available
    try:
        from moviepy.editor import VideoFileClip
    except ImportError:
        print("Error: moviepy library is not installed.")
        print("Please install it using: pip install moviepy")
        sys.exit(1)
    
    # Split the video
    success = split_video(
        args.input_video,
        args.output_dir,
        args.segment_duration
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
